# 技术架构设计文档 - 7路到12路灰度传感器移植

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-02
- **负责人**: Bob (架构师)
- **项目代号**: Gray_Sensor_Migration_Architecture
- **版权归属**: 米醋电子工作室

## 2. 架构概述

### 2.1 设计目标
- **无缝替换**: 12路I2C灰度传感器完全替换7路GPIO传感器
- **接口兼容**: 保持上层应用接口完全不变
- **性能保证**: 确保实时性和精度要求
- **可维护性**: 清晰的分层架构，便于后续维护

### 2.2 核心设计原则
- **分层架构**: 硬件抽象层、数据处理层、应用逻辑层
- **单一职责**: 每个模块职责明确，耦合度低
- **向后兼容**: 保持现有接口和调用方式不变
- **容错设计**: 完善的错误处理和恢复机制

## 3. 系统架构设计

### 3.1 整体架构图

```
┌─────────────────────────────────────────────────────────┐
│                   应用逻辑层 (Application Layer)          │
├─────────────────────────────────────────────────────────┤
│  Gray_Task()           │  权重计算算法    │  PID接口      │
│  - 循迹逻辑            │  - 偏差计算      │  - 控制输出    │
│  - 左转检测            │  - 权重数组      │  - 状态管理    │
└─────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────┐
│                 数据处理层 (Data Processing Layer)        │
├─────────────────────────────────────────────────────────┤
│  Digtal_Get()          │  数据映射算法    │  错误处理      │
│  - 接口适配            │  - 12路->7路     │  - 重试机制    │
│  - 数据验证            │  - 位操作优化    │  - 备份数据    │
└─────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────┐
│                  硬件抽象层 (Hardware Abstraction Layer)  │
├─────────────────────────────────────────────────────────┤
│  soft_i2c.c/h          │  nchd12.c/h      │  GPIO配置     │
│  - I2C通信协议         │  - PCA9555驱动   │  - 引脚初始化  │
│  - 时序控制            │  - 寄存器操作    │  - 中断配置    │
└─────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────┐
│                      硬件层 (Hardware Layer)             │
├─────────────────────────────────────────────────────────┤
│  MSPM0G3507           │  12路灰度传感器   │  I2C总线      │
│  - PA0(SCL)           │  - NCHD12模块     │  - 地址0x40   │
│  - PA1(SDA)           │  - PCA9555芯片    │  - 100kHz     │
└─────────────────────────────────────────────────────────┘
```

### 3.2 模块职责划分

#### 3.2.1 硬件抽象层 (HAL)
**模块**: soft_i2c.c/h
- **职责**: 提供软件I2C通信功能
- **接口**:
  ```c
  void _bsp_analog_i2c_start(void);
  void _bsp_analog_i2c_stop(void);
  uint8_t _bsp_analog_i2c_send_byte_nask(uint8_t data);
  uint8_t _bsp_analog_i2c_read_byte(void);
  uint8_t _bsp_analog_i2c_wait_ack(void);
  void _bsp_analog_i2c_ack(void);
  void _bsp_analog_i2c_nack(void);
  ```

**模块**: nchd12.c/h
- **职责**: PCA9555芯片驱动，提供12路数据读取
- **接口**:
  ```c
  uint16_t pca9555_read_bit12(uint8_t slave_num);
  void i2c_CheckDevice(uint8_t addr);
  ```

#### 3.2.2 数据处理层
**模块**: 修改后的gray.c中的Digtal_Get()函数
- **职责**: 数据获取、映射和验证
- **核心算法**:
  ```c
  unsigned char Digtal_Get(void)
  {
      static unsigned char backup_data = 0;
      uint16_t raw_data = pca9555_read_bit12(0x40);
      
      // 数据有效性检查
      if(raw_data == 0x0000 || raw_data == 0x0FFF) {
          return backup_data;  // 使用备份数据
      }
      
      // 12路到7路映射：选择中间7路(bit3-bit9)
      unsigned char mapped_data = (raw_data >> 3) & 0x7F;
      backup_data = mapped_data;
      
      return mapped_data;
  }
  ```

#### 3.2.3 应用逻辑层
**模块**: Gray_Task()函数 (保持不变)
- **职责**: 循迹算法和权重计算
- **保持原有逻辑**:
  ```c
  void Gray_Task(void)
  {
      Digtal = ~Digtal_Get();  // 接口调用不变
      
      // 权重计算逻辑完全保持不变
      float weighted_sum = 0;
      uint8_t black_line_count = 0;
      
      for(uint8_t i = 0; i < 7; i++) {
          if((Digtal>>i) & 0x01) {
              weighted_sum += gray_weights[6-i];
              black_line_count++;
          }
      }
      
      if(black_line_count > 0)
          g_line_position_error = weighted_sum / (float)black_line_count;
      else
          g_line_position_error = 0;
  }
  ```

## 4. 关键技术决策

### 4.1 数据映射策略

#### 4.1.1 映射算法选择
**选择方案**: 中间7路映射 (bit3-bit9)
**理由**:
- 中间位置传感器对循迹最关键
- 避免边缘传感器的干扰
- 保持对称性，确保左右平衡

#### 4.1.2 映射实现
```c
// 12路传感器布局: [0][1][2][3][4][5][6][7][8][9][10][11]
// 选择中间7路:              [3][4][5][6][7][8][9]
// 映射到7路权重:           [-3][-2][-1][0][1][2][3]

uint16_t raw_12bit = pca9555_read_bit12(0x40);
uint8_t mapped_7bit = (raw_12bit >> 3) & 0x7F;
```

### 4.2 I2C通信优化

#### 4.2.1 软件I2C vs 硬件I2C
**选择**: 软件I2C
**理由**:
- 时序完全可控，避免硬件I2C的复杂配置
- 与12路项目保持一致，降低移植风险
- 引脚配置灵活，便于调试

#### 4.2.2 通信时序优化
- **时钟频率**: 100kHz (标准模式)
- **数据传输时间**: 约0.2ms (16位数据)
- **占用调度时间**: 2% (0.2ms/10ms)
- **性能影响**: 可忽略

### 4.3 错误处理机制

#### 4.3.1 三级错误处理
1. **第一级 - 重试机制**:
   ```c
   for(int retry = 0; retry < 3; retry++) {
       uint16_t data = pca9555_read_bit12(0x40);
       if(data != 0x0000 && data != 0x0FFF) {
           return data;  // 数据有效
       }
   }
   ```

2. **第二级 - 备份数据**:
   ```c
   static uint16_t backup_data = 0x0060;  // 中间位置默认值
   return backup_data;
   ```

3. **第三级 - 系统报警**:
   ```c
   // 可选：通过串口或LED指示通信异常
   error_count++;
   if(error_count > 100) {
       // 系统级错误处理
   }
   ```

#### 4.3.2 数据有效性检查
```c
bool is_data_valid(uint16_t data) {
    // 检查全0或全1的异常情况
    if(data == 0x0000 || data == 0x0FFF) return false;
    
    // 检查数据跳变是否过大
    static uint16_t last_data = 0x0060;
    uint16_t diff = abs(data - last_data);
    if(diff > 0x0200) return false;  // 跳变阈值
    
    last_data = data;
    return true;
}
```

## 5. 系统配置设计

### 5.1 GPIO配置更新

#### 5.1.1 ti_msp_dl_config.h 新增定义
```c
/* I2C引脚定义 */
#define I2C_SCL_PORT                                                     (GPIOA)
#define I2C_SCL_PIN                                              (DL_GPIO_PIN_0)
#define I2C_SCL_IOMUX                                             (IOMUX_PINCM1)

#define I2C_SDA_PORT                                                     (GPIOA)
#define I2C_SDA_PIN                                              (DL_GPIO_PIN_1)
#define I2C_SDA_IOMUX                                             (IOMUX_PINCM2)

/* PCA9555芯片地址定义 */
#define PCA9555_ADDR_WRITE                                               (0x40)
#define PCA9555_ADDR_READ                                                (0x41)
```

#### 5.1.2 empty.syscfg 配置更新
```javascript
// 添加I2C引脚配置
GPIO_I2C.$name                              = "I2C_PINS";
GPIO_I2C.port                               = "PORTA";
GPIO_I2C.associatedPins.create(2);
GPIO_I2C.associatedPins[0].$name            = "SCL";
GPIO_I2C.associatedPins[0].direction        = "OUTPUT";
GPIO_I2C.associatedPins[0].initialValue     = "SET";
GPIO_I2C.associatedPins[0].pin.$assign      = "PA0";
GPIO_I2C.associatedPins[1].$name            = "SDA";
GPIO_I2C.associatedPins[1].direction        = "OUTPUT";
GPIO_I2C.associatedPins[1].initialValue     = "SET";
GPIO_I2C.associatedPins[1].pin.$assign      = "PA1";
```

### 5.2 初始化序列设计

#### 5.2.1 系统初始化顺序
```c
void user_config(void)
{
    // 原有初始化代码...
    
    // 新增I2C初始化
    i2c_gpio_init();           // I2C引脚初始化
    i2c_CheckDevice(0x40);     // PCA9555设备检测
    pca9555_config_input();    // 配置为输入模式
    
    // 原有初始化代码继续...
}
```

#### 5.2.2 PCA9555初始化配置
```c
void pca9555_config_input(void)
{
    // 配置所有引脚为输入模式
    _bsp_analog_i2c_start();
    _bsp_analog_i2c_send_byte_nask(0x40);  // 写地址
    _bsp_analog_i2c_wait_ack();
    _bsp_analog_i2c_send_byte_nask(CONFIG_PORT_REGISTER0);  // 配置寄存器0
    _bsp_analog_i2c_wait_ack();
    _bsp_analog_i2c_send_byte_nask(0xFF);  // 全部设为输入
    _bsp_analog_i2c_wait_ack();
    _bsp_analog_i2c_send_byte_nask(0x0F);  // 高4位设为输入
    _bsp_analog_i2c_wait_ack();
    _bsp_analog_i2c_stop();
}
```

## 6. 性能分析与优化

### 6.1 时序分析

#### 6.1.1 I2C通信时序
- **启动条件**: 2μs
- **地址传输**: 8 × 10μs = 80μs
- **寄存器地址**: 8 × 10μs = 80μs
- **重启动条件**: 2μs
- **数据读取**: 16 × 10μs = 160μs
- **停止条件**: 2μs
- **总计**: 约326μs ≈ 0.33ms

#### 6.1.2 调度周期影响
- **调度周期**: 10ms
- **I2C占用**: 0.33ms
- **占用比例**: 3.3%
- **剩余时间**: 9.67ms (足够其他任务)

### 6.2 内存使用分析

#### 6.2.1 代码空间 (Flash)
- **soft_i2c.c**: 约800字节
- **nchd12.c**: 约300字节
- **修改的gray.c**: 增加约100字节
- **总增加**: 约1.2KB

#### 6.2.2 数据空间 (RAM)
- **静态变量**: 约50字节
- **栈空间**: 约30字节
- **总增加**: 约80字节

### 6.3 优化策略

#### 6.3.1 代码优化
- 使用内联函数减少函数调用开销
- 位操作优化数据映射算法
- 编译器优化选项设置

#### 6.3.2 实时性优化
- I2C通信与其他任务错开执行
- 使用DMA减少CPU占用 (可选)
- 中断优先级合理配置

## 7. 集成测试策略

### 7.1 单元测试

#### 7.1.1 I2C通信测试
```c
void test_i2c_communication(void)
{
    // 测试设备检测
    assert(i2c_CheckDevice(0x40) == SUCCESS);
    
    // 测试数据读取
    uint16_t data = pca9555_read_bit12(0x40);
    assert(data != 0x0000 && data != 0x0FFF);
    
    // 测试重复读取一致性
    uint16_t data2 = pca9555_read_bit12(0x40);
    assert(abs(data - data2) < 0x0010);  // 允许小幅波动
}
```

#### 7.1.2 数据映射测试
```c
void test_data_mapping(void)
{
    // 测试边界条件
    assert(map_12_to_7(0x0000) == 0x00);
    assert(map_12_to_7(0x0FFF) == 0x7F);
    
    // 测试中间值
    assert(map_12_to_7(0x01F8) == 0x3F);  // bit3-bit9全为1
    
    // 测试对称性
    uint16_t left_pattern = 0x0038;   // 左侧3位
    uint16_t right_pattern = 0x01C0;  // 右侧3位
    assert(map_12_to_7(left_pattern) != map_12_to_7(right_pattern));
}
```

### 7.2 集成测试

#### 7.2.1 循迹精度测试
- 标准测试轨道：直线、弯道、交叉路口
- 精度对比：新方案 vs 原7路方案
- 误差范围：偏差值误差 ≤ 5%

#### 7.2.2 实时性测试
- 调度周期监控：确保10ms周期稳定
- 响应时间测试：传感器变化到控制输出的延迟
- 长期稳定性：连续运行24小时无异常

## 8. 风险评估与缓解

### 8.1 技术风险

#### 8.1.1 I2C通信稳定性
**风险等级**: 中等
**影响**: 数据读取失败，循迹异常
**缓解措施**:
- 完善的重试机制
- 备份数据策略
- 通信状态监控

#### 8.1.2 数据映射精度
**风险等级**: 中等
**影响**: 循迹精度下降
**缓解措施**:
- 多种映射算法对比测试
- 权重系数微调
- 实际轨道验证

### 8.2 集成风险

#### 8.2.1 接口兼容性
**风险等级**: 低
**影响**: 编译错误，功能异常
**缓解措施**:
- 严格保持接口一致性
- 分阶段集成测试
- 完整的回归测试

#### 8.2.2 性能影响
**风险等级**: 低
**影响**: 系统响应变慢
**缓解措施**:
- 详细的性能分析
- 优化关键路径
- 实时监控机制

## 9. 部署与维护

### 9.1 部署检查清单
- [ ] 硬件连接正确 (PA0-SCL, PA1-SDA)
- [ ] I2C地址配置正确 (0x40)
- [ ] 代码编译无错误无警告
- [ ] 功能测试通过
- [ ] 性能测试达标

### 9.2 维护指南
- **日常监控**: I2C通信状态，循迹精度
- **故障诊断**: 通信失败，数据异常处理
- **升级路径**: 硬件升级，算法优化
- **文档更新**: 及时更新技术文档

## 10. 总结

本架构设计通过分层设计和接口抽象，实现了12路I2C灰度传感器对7路GPIO传感器的无缝替换。关键技术决策包括：

1. **三层架构设计**: 确保职责清晰，便于维护
2. **智能数据映射**: 中间7路选择策略，保证循迹精度
3. **完善错误处理**: 三级容错机制，确保系统稳定
4. **性能优化**: I2C通信优化，满足实时性要求

该架构设计已充分考虑了技术可行性、性能要求和维护便利性，为后续的开发实施提供了坚实的技术基础。

---

**文档状态**: 已完成
**下一步行动**: 开始详细的开发任务执行