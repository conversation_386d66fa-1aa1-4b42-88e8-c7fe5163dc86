# PRD - 7路到12路灰度传感器移植项目

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-02
- **负责人**: Emma (产品经理)
- **项目代号**: Gray_Sensor_Migration
- **版权归属**: 米醋电子工作室

## 2. 背景与问题陈述

### 2.1 项目背景
ti_template项目是一个基于MSPM0G3507的循迹小车控制系统，原本使用7路灰度传感器进行路径检测。由于硬件故障，7路灰度传感器已损坏无法使用。

### 2.2 问题描述
- **硬件故障**: 原有7路灰度传感器硬件损坏，无法继续使用
- **功能需求**: 需要保持原有循迹功能完全不变
- **技术约束**: 必须在现有ti_template项目架构基础上进行替换
- **兼容性要求**: 确保与现有PID控制、电机驱动等模块完全兼容

### 2.3 解决方案概述
使用12路灰度传感器检测（MSPM0G3507）项目中的12路I2C灰度传感器方案替换原有的7路GPIO灰度传感器，通过数据映射算法保持循迹逻辑一致性。

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **硬件替换**: 成功将7路GPIO灰度传感器替换为12路I2C灰度传感器
2. **功能保持**: 循迹功能与原7路传感器效果完全一致
3. **架构兼容**: 保持ti_template项目的整体架构和其他功能模块不变
4. **编译兼容**: 确保代码能在Keil5环境下正常编译运行

### 3.2 关键结果 (Key Results)
- **KR1**: 12路灰度传感器I2C通信成功率 ≥ 99.9%
- **KR2**: 循迹偏差计算精度与原7路传感器误差 ≤ 5%
- **KR3**: 系统响应时间保持在10ms调度周期内
- **KR4**: Keil5编译零错误零警告
- **KR5**: 硬件兼容性测试通过率100%

### 3.3 反向指标 (Counter Metrics)
- 代码复杂度不应显著增加（≤20%）
- 内存使用量不应增加（保持现有水平）
- 系统稳定性不应下降

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**: 嵌入式开发工程师
- **使用场景**: 循迹小车开发和调试
- **技术背景**: 熟悉MSPM0G3507开发，了解循迹算法

### 4.2 用户故事
- **故事1**: 作为开发工程师，我希望能够快速替换损坏的灰度传感器，以便继续进行循迹功能开发
- **故事2**: 作为系统集成者，我希望新的传感器方案不影响现有的PID控制和电机驱动功能
- **故事3**: 作为测试工程师，我希望新方案的循迹精度与原方案保持一致

## 5. 功能规格详述

### 5.1 核心功能模块

#### 5.1.1 I2C通信模块
- **功能描述**: 提供与12路灰度传感器的I2C通信接口
- **技术规格**: 
  - I2C地址: 0x40 (写) / 0x41 (读)
  - 通信速率: 标准模式 (100kHz)
  - 引脚配置: PA0(SCL), PA1(SDA)
- **接口定义**:
  ```c
  uint16_t pca9555_read_bit12(uint8_t slave_num);
  void i2c_CheckDevice(uint8_t addr);
  ```

#### 5.1.2 数据映射模块
- **功能描述**: 将12路传感器数据映射为7路格式
- **映射算法**: 选择中间7路数据 (bit3-bit9)
- **数据格式**: 保持原有的8位数据格式
- **接口定义**:
  ```c
  unsigned char Digtal_Get(void);  // 保持原接口不变
  ```

#### 5.1.3 权重计算模块
- **功能描述**: 基于7路数据计算循迹偏差
- **权重数组**: {-6.0f, -4.5, -2.0f, 0.0f, 2.0f, 4.5f, 6.0f}
- **计算公式**: g_line_position_error = weighted_sum / black_line_count
- **接口定义**:
  ```c
  void Gray_Task(void);  // 保持原接口不变
  extern float g_line_position_error;
  ```

### 5.2 业务逻辑规则

#### 5.2.1 数据处理流程
1. I2C读取12路原始数据
2. 提取中间7路数据 (bit3-bit9)
3. 数据取反处理 (Digtal = ~Digtal_Get())
4. 权重计算得到偏差值
5. 传递给PID控制器

#### 5.2.2 错误处理规则
- I2C通信失败时使用上次有效数据
- 传感器全黑或全白时偏差值为0
- 异常数据过滤和平滑处理

### 5.3 边缘情况与异常处理

#### 5.3.1 硬件异常
- **I2C通信失败**: 重试机制，最多3次
- **传感器断线**: 使用备份数据，报警提示
- **电源不稳定**: 增加滤波和稳定性检测

#### 5.3.2 数据异常
- **全0数据**: 判断为传感器故障，使用历史数据
- **全1数据**: 判断为环境异常，保持当前状态
- **跳变数据**: 数据平滑处理，避免突变

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- ✅ I2C通信驱动集成
- ✅ 12路到7路数据映射
- ✅ 原有循迹算法保持
- ✅ 系统配置文件更新
- ✅ Keil5编译兼容性
- ✅ 硬件连接适配

### 6.2 排除功能 (Out of Scope)
- ❌ PID控制算法修改
- ❌ 电机驱动逻辑变更
- ❌ 调度器架构调整
- ❌ OLED显示功能修改
- ❌ 编码器处理逻辑变更
- ❌ 串口通信功能修改

## 7. 依赖与风险

### 7.1 内部依赖项
- **硬件依赖**: MSPM0G3507开发板，12路NCHD12灰度传感器
- **软件依赖**: Keil5开发环境，TI SDK库
- **模块依赖**: PID控制模块，电机驱动模块，调度器模块

### 7.2 外部依赖项
- **硬件供应**: 12路灰度传感器的稳定供应
- **技术支持**: TI官方技术文档和支持
- **开发工具**: Keil5许可证和更新

### 7.3 潜在风险

#### 7.3.1 技术风险
- **风险**: I2C通信稳定性问题
- **影响**: 数据读取失败，循迹精度下降
- **缓解措施**: 增加重试机制，优化I2C时序

#### 7.3.2 兼容性风险
- **风险**: 数据映射算法精度不足
- **影响**: 循迹效果与原方案差异较大
- **缓解措施**: 多种映射算法对比测试，选择最优方案

#### 7.3.3 集成风险
- **风险**: 与现有模块接口不兼容
- **影响**: 系统功能异常，需要大量修改
- **缓解措施**: 严格保持接口一致性，分阶段集成测试

## 8. 发布初步计划

### 8.1 开发阶段
- **阶段1**: I2C驱动集成 (1天)
- **阶段2**: 系统配置更新 (0.5天)
- **阶段3**: 数据获取函数重写 (1天)
- **阶段4**: 算法验证测试 (1天)
- **阶段5**: 编译测试和功能验证 (1天)

### 8.2 测试计划
- **单元测试**: 各模块独立功能测试
- **集成测试**: 模块间接口兼容性测试
- **系统测试**: 整体循迹功能验证
- **性能测试**: 响应时间和精度测试

### 8.3 发布策略
- **内部测试**: 开发环境完整功能验证
- **硬件测试**: 实际硬件平台功能测试
- **性能优化**: 根据测试结果进行优化
- **正式发布**: 完整功能交付

## 9. 数据跟踪与监控

### 9.1 关键指标监控
- I2C通信成功率
- 循迹偏差计算精度
- 系统响应时间
- 内存使用情况

### 9.2 质量保证
- 代码审查机制
- 自动化测试覆盖
- 性能基准对比
- 长期稳定性测试

---

**文档状态**: 已完成
**下一步行动**: 开始技术架构设计和任务分解