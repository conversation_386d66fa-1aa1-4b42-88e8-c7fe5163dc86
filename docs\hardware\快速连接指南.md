# 12路灰度传感器快速连接指南

## 🚀 快速连接 (4根线)

### 📌 连接表
| 核心板引脚 | 传感器引脚 | 线色建议 | 功能 |
|-----------|-----------|----------|------|
| **PA0 (Pin 33)** | **SCL** | 🟡 黄色 | I2C时钟 |
| **PA1 (Pin 34)** | **SDA** | 🟢 绿色 | I2C数据 |
| **3.3V** | **VCC** | 🔴 红色 | 电源 |
| **GND** | **GND** | ⚫ 黑色 | 地线 |

## ⚡ 30秒连接步骤

1. **断电** - 确保开发板断电
2. **红线** - 3.3V → VCC (电源)
3. **黑线** - GND → GND (地线)  
4. **黄线** - PA0 → SCL (时钟)
5. **绿线** - PA1 → SDA (数据)
6. **检查** - 确认连接牢固
7. **上电** - 开发板上电测试

## 🔧 验证代码
```c
// 在main函数中添加测试代码
uint8_t result = i2c_CheckDevice(0x40);
if(result == 0) {
    printf("✅ 传感器连接成功!\n");
} else {
    printf("❌ 传感器连接失败!\n");
}
```

## ⚠️ 重要提醒
- ✅ **电压**: 必须使用3.3V，不能用5V
- ✅ **极性**: 红线接VCC，黑线接GND
- ✅ **引脚**: PA0接SCL，PA1接SDA
- ✅ **固定**: 确保连接牢固不松动

## 🎯 传感器布局
```
12路传感器: [S0][S1][S2][S3][S4][S5][S6][S7][S8][S9][S10][S11]
使用中间7路:              [S3][S4][S5][S6][S7][S8][S9]
```

连接完成后，传感器将自动使用中间7路进行循迹，与原7路传感器效果完全一致！