#include "gray.h"

unsigned char Digtal;
float gray_weights[7] = {-6.0f, -4.5, -2.0f, 0.0f , 2.0f,4.5f,6.0f}; // 7 ·�Ҷ�ͨ��Ȩ�ر�

float g_line_position_error; // ѭ�����ֵ
extern unsigned char L_count;
unsigned char baochi_flag=0;
unsigned char Digtal_Get(void)
{
	static unsigned char backup_data = 0x00;  // 备份数据，用于I2C通信失败时
	static uint8_t error_count = 0;           // 错误计数器

	// 通过I2C读取12路灰度传感器数据
	uint16_t raw_12bit_data = pca9555_read_bit12(0x40);

	// 数据有效性检查
	if(raw_12bit_data == 0x0000 || raw_12bit_data == 0x0FFF) {
		error_count++;
		if(error_count > 3) {
			// 连续错误超过3次，使用备份数据
			return backup_data;
		}
		// 重试读取
		raw_12bit_data = pca9555_read_bit12(0x40);
		if(raw_12bit_data == 0x0000 || raw_12bit_data == 0x0FFF) {
			return backup_data;
		}
	}

	// 数据有效，重置错误计数
	error_count = 0;

	// 12路到7路数据映射：选择中间7路(bit3-bit9)
	// 12路传感器布局: [0][1][2][3][4][5][6][7][8][9][10][11]
	// 选择中间7路:              [3][4][5][6][7][8][9]
	// 映射到7路权重:           [0][1][2][3][4][5][6]
	unsigned char mapped_7bit = (raw_12bit_data >> 3) & 0x7F;

	// 更新备份数据
	backup_data = mapped_7bit;

	return mapped_7bit;
}

void Gray_Task(void)
{
	Digtal=~Digtal_Get();

    float weighted_sum = 0;
    uint8_t black_line_count = 0;

    for(uint8_t i = 0; i < 7; i++)
    {
      if((Digtal>>i) & 0x01)
      {
        weighted_sum += gray_weights[6-i];
        black_line_count++;
      }
    }
    
    if(black_line_count > 0)
      g_line_position_error = weighted_sum / (float)black_line_count;
	else
		g_line_position_error=0;
	if(pid_running&&((Digtal>>6)&0x01)&&((Digtal>>5)&0x01)&&((Digtal>>4)&0x01))
  {
//	  if(baochi_flag==0)
//		 L_count++;
//	  baochi_flag=1;
//	  g_line_position_error=-15;
	  pid_running=0;
	  Motor_Stop();
	  
	  if(++L_count==3)
		  return;
	  Motor_Speed_l(-20);
	  Motor_Speed_r(35);
	  delay_ms(200);
	  pid_running=1;
	  basic_speed=45;
	  pid_reset(&pid_speed_left);
	  pid_reset(&pid_speed_right);	
	  pid_reset(&pid_line_gray);	
	  pid_set_target(&pid_speed_left, basic_speed);
	  pid_set_target(&pid_speed_right, basic_speed);
//	  pid_set_target(&pid_line_gray, 0);
  }
  else
  {
	  baochi_flag=0;
  }
//  my_printf(UART_0_INST,"count:%d\r\n",L_count);
//  	my_printf(UART_0_INST,"error:%.2f Digtal %d-%d-%d-%d-%d-%d-%d\r\n",g_line_position_error,(Digtal>>6)&0x01,(Digtal>>5)&0x01,(Digtal>>4)&0x01,(Digtal>>3)&0x01,(Digtal>>2)&0x01,(Digtal>>1)&0x01,(Digtal>>0)&0x01);

}


